<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsMedicalIcdDirMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO" id="ClmsMedicalIcdDirMap">
        <id property="id" column="id" />
        <result property="dianoseCode" column="dianose_code" />
        <result property="dirId" column="dir_id" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, dianose_code, dir_id, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE 1=1
        <if test="dianoseCode != null and dianoseCode != ''">
            AND dianose_code = #{dianoseCode,jdbcType=VARCHAR}
        </if>
        <if test="dirId != null">
            AND dir_id = #{dirId,jdbcType=INTEGER}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByDianoseCode" parameterType="java.lang.String" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE dianose_code = #{dianoseCode,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByDirId" parameterType="java.lang.Integer" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE dir_id = #{dirId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByDianoseCodes" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE dianose_code IN
        <foreach collection="dianoseCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByDirIds" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE dir_id IN
        <foreach collection="dirIds" item="dirId" open="(" separator="," close=")">
            #{dirId}
        </foreach>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsMedicalIcdDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_ICD_DIR
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_MEDICAL_ICD_DIR
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICAL_ICD_DIR (
            dianose_code, dir_id, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{dianoseCode,jdbcType=VARCHAR}, #{dirId,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
            #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICAL_ICD_DIR
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dianoseCode != null">dianose_code,</if>
            <if test="dirId != null">dir_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dianoseCode != null">#{dianoseCode,jdbcType=VARCHAR},</if>
            <if test="dirId != null">#{dirId,jdbcType=INTEGER},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO">
        UPDATE CLMS_MEDICAL_ICD_DIR
        <set>
            <if test="dianoseCode != null">dianose_code = #{dianoseCode,jdbcType=VARCHAR},</if>
            <if test="dirId != null">dir_id = #{dirId,jdbcType=INTEGER},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO">
        UPDATE CLMS_MEDICAL_ICD_DIR
        SET dianose_code = #{dianoseCode,jdbcType=VARCHAR},
            dir_id = #{dirId,jdbcType=INTEGER},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
