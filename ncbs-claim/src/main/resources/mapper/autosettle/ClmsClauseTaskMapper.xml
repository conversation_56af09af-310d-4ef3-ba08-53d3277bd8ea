<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseTaskMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO" id="ClmsClauseTaskMap">
        <id property="id" column="id" />
        <result property="flowInId" column="flow_in_id" />
        <result property="configId" column="config_id" />
        <result property="versionNo" column="version_no" />
        <result property="taskNode" column="task_node" />
        <result property="flowInTime" column="flow_in_time" />
        <result property="startTime" column="start_time" />
        <result property="flowOutTime" column="flow_out_time" />
        <result property="nodeStatus" column="node_status" />
        <result property="taskUserCode" column="task_user_code" />
        <result property="reviewOpinion" column="review_opinion" />
        <result property="remark" column="remark" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, flow_in_id, config_id, version_no, task_node, flow_in_time, start_time, 
        flow_out_time, node_status, task_user_code, review_opinion, remark, 
        created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE 1=1
        <if test="configId != null">
            AND config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="flowInId != null">
            AND flow_in_id = #{flowInId,jdbcType=INTEGER}
        </if>
        <if test="taskNode != null and taskNode != ''">
            AND task_node = #{taskNode,jdbcType=VARCHAR}
        </if>
        <if test="nodeStatus != null and nodeStatus != ''">
            AND node_status = #{nodeStatus,jdbcType=VARCHAR}
        </if>
        <if test="taskUserCode != null and taskUserCode != ''">
            AND task_user_code = #{taskUserCode,jdbcType=VARCHAR}
        </if>
        <if test="versionNo != null">
            AND version_no = #{versionNo,jdbcType=INTEGER}
        </if>
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByConfigId" parameterType="java.lang.Integer" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE config_id = #{configId,jdbcType=INTEGER}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByFlowInId" parameterType="java.lang.Integer" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE flow_in_id = #{flowInId,jdbcType=INTEGER}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByTaskNode" parameterType="java.lang.String" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE task_node = #{taskNode,jdbcType=VARCHAR}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByNodeStatus" parameterType="java.lang.String" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE node_status = #{nodeStatus,jdbcType=VARCHAR}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByTaskUserCode" parameterType="java.lang.String" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE task_user_code = #{taskUserCode,jdbcType=VARCHAR}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByVersionNo" parameterType="java.lang.Integer" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE version_no = #{versionNo,jdbcType=INTEGER}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByConfigIdAndTaskNode" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE config_id = #{configId,jdbcType=INTEGER}
        AND task_node = #{taskNode,jdbcType=VARCHAR}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByConfigIdAndNodeStatus" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE config_id = #{configId,jdbcType=INTEGER}
        AND node_status = #{nodeStatus,jdbcType=VARCHAR}
        ORDER BY flow_in_time DESC, sys_ctime DESC
    </select>

    <select id="selectByFlowInTimeRange" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND flow_in_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND flow_in_time <= #{endTime}
        </if>
        ORDER BY flow_in_time DESC
    </select>

    <select id="selectByFlowOutTimeRange" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND flow_out_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND flow_out_time <= #{endTime}
        </if>
        ORDER BY flow_out_time DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsClauseTaskMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_TASK
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_CLAUSE_TASK
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_TASK (
            flow_in_id, config_id, version_no, task_node, flow_in_time, start_time, 
            flow_out_time, node_status, task_user_code, review_opinion, remark, 
            created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{flowInId,jdbcType=INTEGER}, #{configId,jdbcType=INTEGER}, #{versionNo,jdbcType=INTEGER}, 
            #{taskNode,jdbcType=VARCHAR}, #{flowInTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, 
            #{flowOutTime,jdbcType=TIMESTAMP}, #{nodeStatus,jdbcType=VARCHAR}, #{taskUserCode,jdbcType=VARCHAR}, 
            #{reviewOpinion,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
            #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowInId != null">flow_in_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="taskNode != null">task_node,</if>
            <if test="flowInTime != null">flow_in_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="flowOutTime != null">flow_out_time,</if>
            <if test="nodeStatus != null">node_status,</if>
            <if test="taskUserCode != null">task_user_code,</if>
            <if test="reviewOpinion != null">review_opinion,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowInId != null">#{flowInId,jdbcType=INTEGER},</if>
            <if test="configId != null">#{configId,jdbcType=INTEGER},</if>
            <if test="versionNo != null">#{versionNo,jdbcType=INTEGER},</if>
            <if test="taskNode != null">#{taskNode,jdbcType=VARCHAR},</if>
            <if test="flowInTime != null">#{flowInTime,jdbcType=TIMESTAMP},</if>
            <if test="startTime != null">#{startTime,jdbcType=TIMESTAMP},</if>
            <if test="flowOutTime != null">#{flowOutTime,jdbcType=TIMESTAMP},</if>
            <if test="nodeStatus != null">#{nodeStatus,jdbcType=VARCHAR},</if>
            <if test="taskUserCode != null">#{taskUserCode,jdbcType=VARCHAR},</if>
            <if test="reviewOpinion != null">#{reviewOpinion,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO">
        UPDATE CLMS_CLAUSE_TASK
        <set>
            <if test="flowInId != null">flow_in_id = #{flowInId,jdbcType=INTEGER},</if>
            <if test="configId != null">config_id = #{configId,jdbcType=INTEGER},</if>
            <if test="versionNo != null">version_no = #{versionNo,jdbcType=INTEGER},</if>
            <if test="taskNode != null">task_node = #{taskNode,jdbcType=VARCHAR},</if>
            <if test="flowInTime != null">flow_in_time = #{flowInTime,jdbcType=TIMESTAMP},</if>
            <if test="startTime != null">start_time = #{startTime,jdbcType=TIMESTAMP},</if>
            <if test="flowOutTime != null">flow_out_time = #{flowOutTime,jdbcType=TIMESTAMP},</if>
            <if test="nodeStatus != null">node_status = #{nodeStatus,jdbcType=VARCHAR},</if>
            <if test="taskUserCode != null">task_user_code = #{taskUserCode,jdbcType=VARCHAR},</if>
            <if test="reviewOpinion != null">review_opinion = #{reviewOpinion,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO">
        UPDATE CLMS_CLAUSE_TASK
        SET flow_in_id = #{flowInId,jdbcType=INTEGER},
            config_id = #{configId,jdbcType=INTEGER},
            version_no = #{versionNo,jdbcType=INTEGER},
            task_node = #{taskNode,jdbcType=VARCHAR},
            flow_in_time = #{flowInTime,jdbcType=TIMESTAMP},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            flow_out_time = #{flowOutTime,jdbcType=TIMESTAMP},
            node_status = #{nodeStatus,jdbcType=VARCHAR},
            task_user_code = #{taskUserCode,jdbcType=VARCHAR},
            review_opinion = #{reviewOpinion,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
