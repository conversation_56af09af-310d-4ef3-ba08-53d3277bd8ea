<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseMatchMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO" id="ClmsClauseMatchMap">
        <id property="id" column="id" />
        <result property="configId" column="config_id" />
        <result property="matchSequece" column="match_sequece" />
        <result property="productCode" column="product_code" />
        <result property="productName" column="product_name" />
        <result property="planCode" column="plan_code" />
        <result property="planName" column="plan_name" />
        <result property="riskGroupCode" column="risk_group_code" />
        <result property="riskGroupName" column="risk_group_name" />
        <result property="dutyCode" column="duty_code" />
        <result property="dutyName" column="duty_name" />
        <result property="dutyDetailCode" column="duty_detail_code" />
        <result property="dutyDetailName" column="duty_detail_name" />
        <result property="billType" column="bill_type" />
        <result property="matchKeys" column="match_keys" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, config_id, match_sequece, product_code, product_name, plan_code, plan_name, 
        risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, 
        duty_detail_name, bill_type, match_keys, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE 1=1
        <if test="configId != null">
            AND config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="productCode != null and productCode != ''">
            AND product_code = #{productCode,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null and planCode != ''">
            AND plan_code = #{planCode,jdbcType=VARCHAR}
        </if>
        <if test="riskGroupCode != null and riskGroupCode != ''">
            AND risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyCode != null and dutyCode != ''">
            AND duty_code = #{dutyCode,jdbcType=VARCHAR}
        </if>
        <if test="dutyDetailCode != null and dutyDetailCode != ''">
            AND duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR}
        </if>
        <if test="billType != null and billType != ''">
            AND bill_type = #{billType,jdbcType=VARCHAR}
        </if>
        <if test="matchKeys != null and matchKeys != ''">
            AND match_keys LIKE CONCAT('%', #{matchKeys}, '%')
        </if>
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByConfigId" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE config_id = #{configId,jdbcType=INTEGER}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByProductCode" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByPlanCode" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE plan_code = #{planCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByRiskGroupCode" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByDutyCode" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE duty_code = #{dutyCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByDutyDetailCode" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByBillType" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE bill_type = #{billType,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByProductAndPlan" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        AND plan_code = #{planCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByProductPlanAndRisk" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        AND plan_code = #{planCode,jdbcType=VARCHAR}
        AND risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByConfigIdOrderBySequence" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE config_id = #{configId,jdbcType=INTEGER}
        ORDER BY match_sequece ASC
    </select>

    <select id="selectByMatchKeys" parameterType="java.lang.String" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE match_keys LIKE CONCAT('%', #{matchKeys}, '%')
        ORDER BY match_sequece ASC, sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsClauseMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_CLAUSE_MATCH
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_MATCH (
            config_id, match_sequece, product_code, product_name, plan_code, plan_name, 
            risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, 
            duty_detail_name, bill_type, match_keys, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{configId,jdbcType=INTEGER}, #{matchSequece,jdbcType=INTEGER}, #{productCode,jdbcType=VARCHAR}, 
            #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, 
            #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, 
            #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, 
            #{billType,jdbcType=VARCHAR}, #{matchKeys,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
            #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_MATCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="matchSequece != null">match_sequece,</if>
            <if test="productCode != null">product_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="riskGroupCode != null">risk_group_code,</if>
            <if test="riskGroupName != null">risk_group_name,</if>
            <if test="dutyCode != null">duty_code,</if>
            <if test="dutyName != null">duty_name,</if>
            <if test="dutyDetailCode != null">duty_detail_code,</if>
            <if test="dutyDetailName != null">duty_detail_name,</if>
            <if test="billType != null">bill_type,</if>
            <if test="matchKeys != null">match_keys,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId,jdbcType=INTEGER},</if>
            <if test="matchSequece != null">#{matchSequece,jdbcType=INTEGER},</if>
            <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="planCode != null">#{planCode,jdbcType=VARCHAR},</if>
            <if test="planName != null">#{planName,jdbcType=VARCHAR},</if>
            <if test="riskGroupCode != null">#{riskGroupCode,jdbcType=VARCHAR},</if>
            <if test="riskGroupName != null">#{riskGroupName,jdbcType=VARCHAR},</if>
            <if test="dutyCode != null">#{dutyCode,jdbcType=VARCHAR},</if>
            <if test="dutyName != null">#{dutyName,jdbcType=VARCHAR},</if>
            <if test="dutyDetailCode != null">#{dutyDetailCode,jdbcType=VARCHAR},</if>
            <if test="dutyDetailName != null">#{dutyDetailName,jdbcType=VARCHAR},</if>
            <if test="billType != null">#{billType,jdbcType=VARCHAR},</if>
            <if test="matchKeys != null">#{matchKeys,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO">
        UPDATE CLMS_CLAUSE_MATCH
        <set>
            <if test="configId != null">config_id = #{configId,jdbcType=INTEGER},</if>
            <if test="matchSequece != null">match_sequece = #{matchSequece,jdbcType=INTEGER},</if>
            <if test="productCode != null">product_code = #{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">product_name = #{productName,jdbcType=VARCHAR},</if>
            <if test="planCode != null">plan_code = #{planCode,jdbcType=VARCHAR},</if>
            <if test="planName != null">plan_name = #{planName,jdbcType=VARCHAR},</if>
            <if test="riskGroupCode != null">risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},</if>
            <if test="riskGroupName != null">risk_group_name = #{riskGroupName,jdbcType=VARCHAR},</if>
            <if test="dutyCode != null">duty_code = #{dutyCode,jdbcType=VARCHAR},</if>
            <if test="dutyName != null">duty_name = #{dutyName,jdbcType=VARCHAR},</if>
            <if test="dutyDetailCode != null">duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},</if>
            <if test="dutyDetailName != null">duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},</if>
            <if test="billType != null">bill_type = #{billType,jdbcType=VARCHAR},</if>
            <if test="matchKeys != null">match_keys = #{matchKeys,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO">
        UPDATE CLMS_CLAUSE_MATCH
        SET config_id = #{configId,jdbcType=INTEGER},
            match_sequece = #{matchSequece,jdbcType=INTEGER},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            bill_type = #{billType,jdbcType=VARCHAR},
            match_keys = #{matchKeys,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
