<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsInvoiceMatchMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO" id="ClmsInvoiceMatchMap">
        <id property="id" column="id" />
        <result property="reportNo" column="report_no" />
        <result property="caseTimes" column="case_times" />
        <result property="matchType" column="match_type" />
        <result property="configId" column="config_id" />
        <result property="idAhcsInvoiceInfo" column="id_ahcs_invoice_info" />
        <result property="matchSign" column="match_sign" />
        <result property="failDesc" column="fail_desc" />
        <result property="matchResult" column="match_result" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, report_no, case_times, match_type, config_id, id_ahcs_invoice_info, 
        match_sign, fail_desc, match_result, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE 1=1
        <if test="reportNo != null and reportNo != ''">
            AND report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null">
            AND case_times = #{caseTimes,jdbcType=INTEGER}
        </if>
        <if test="matchType != null and matchType != ''">
            AND match_type = #{matchType,jdbcType=VARCHAR}
        </if>
        <if test="configId != null">
            AND config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="matchSign != null">
            AND match_sign = #{matchSign,jdbcType=INTEGER}
        </if>
        <if test="matchResult != null and matchResult != ''">
            AND match_result = #{matchResult,jdbcType=VARCHAR}
        </if>
        <if test="idAhcsInvoiceInfo != null and idAhcsInvoiceInfo != ''">
            AND id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByReportNo" parameterType="java.lang.String" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        ORDER BY case_times DESC, sys_ctime DESC
    </select>

    <select id="selectByConfigId" parameterType="java.lang.Integer" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE config_id = #{configId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMatchType" parameterType="java.lang.String" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE match_type = #{matchType,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMatchSign" parameterType="java.lang.Integer" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE match_sign = #{matchSign,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMatchResult" parameterType="java.lang.String" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE match_result = #{matchResult,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByIdAhcsInvoiceInfo" parameterType="java.lang.String" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByReportNoAndCaseTimes" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        AND case_times = #{caseTimes,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByReportNoAndMatchType" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        AND match_type = #{matchType,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByReportNoAndMatchSign" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        AND match_sign = #{matchSign,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectFailedMatches" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE match_sign = 0
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectSuccessMatches" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE match_sign = 1
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsInvoiceMatchMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_INVOICE_MATCH
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_INVOICE_MATCH
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_INVOICE_MATCH (
            report_no, case_times, match_type, config_id, id_ahcs_invoice_info, 
            match_sign, fail_desc, match_result, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=INTEGER}, #{matchType,jdbcType=VARCHAR}, 
            #{configId,jdbcType=INTEGER}, #{idAhcsInvoiceInfo,jdbcType=VARCHAR}, #{matchSign,jdbcType=INTEGER}, 
            #{failDesc,jdbcType=VARCHAR}, #{matchResult,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
            #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_INVOICE_MATCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportNo != null">report_no,</if>
            <if test="caseTimes != null">case_times,</if>
            <if test="matchType != null">match_type,</if>
            <if test="configId != null">config_id,</if>
            <if test="idAhcsInvoiceInfo != null">id_ahcs_invoice_info,</if>
            <if test="matchSign != null">match_sign,</if>
            <if test="failDesc != null">fail_desc,</if>
            <if test="matchResult != null">match_result,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportNo != null">#{reportNo,jdbcType=VARCHAR},</if>
            <if test="caseTimes != null">#{caseTimes,jdbcType=INTEGER},</if>
            <if test="matchType != null">#{matchType,jdbcType=VARCHAR},</if>
            <if test="configId != null">#{configId,jdbcType=INTEGER},</if>
            <if test="idAhcsInvoiceInfo != null">#{idAhcsInvoiceInfo,jdbcType=VARCHAR},</if>
            <if test="matchSign != null">#{matchSign,jdbcType=INTEGER},</if>
            <if test="failDesc != null">#{failDesc,jdbcType=VARCHAR},</if>
            <if test="matchResult != null">#{matchResult,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO">
        UPDATE CLMS_INVOICE_MATCH
        <set>
            <if test="reportNo != null">report_no = #{reportNo,jdbcType=VARCHAR},</if>
            <if test="caseTimes != null">case_times = #{caseTimes,jdbcType=INTEGER},</if>
            <if test="matchType != null">match_type = #{matchType,jdbcType=VARCHAR},</if>
            <if test="configId != null">config_id = #{configId,jdbcType=INTEGER},</if>
            <if test="idAhcsInvoiceInfo != null">id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},</if>
            <if test="matchSign != null">match_sign = #{matchSign,jdbcType=INTEGER},</if>
            <if test="failDesc != null">fail_desc = #{failDesc,jdbcType=VARCHAR},</if>
            <if test="matchResult != null">match_result = #{matchResult,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO">
        UPDATE CLMS_INVOICE_MATCH
        SET report_no = #{reportNo,jdbcType=VARCHAR},
            case_times = #{caseTimes,jdbcType=INTEGER},
            match_type = #{matchType,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            match_sign = #{matchSign,jdbcType=INTEGER},
            fail_desc = #{failDesc,jdbcType=VARCHAR},
            match_result = #{matchResult,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
