<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseConfigMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO" id="ClmsClauseConfigMap">
        <id property="id" column="id" />
        <result property="productCode" column="product_code" />
        <result property="productName" column="product_name" />
        <result property="planCode" column="plan_code" />
        <result property="planName" column="plan_name" />
        <result property="pfVersion" column="pf_version" />
        <result property="riskGroupCode" column="risk_group_code" />
        <result property="riskGroupName" column="risk_group_name" />
        <result property="versionNo" column="version_no" />
        <result property="defineSource" column="define_source" />
        <result property="dutyDetail" column="duty_detail" />
        <result property="igFlag" column="ig_flag" />
        <result property="validFlag" column="valid_flag" />
        <result property="effectTime" column="effect_time" />
        <result property="expireTime" column="expire_time" />
        <result property="addReason" column="add_reason" />
        <result property="addReasonDesc" column="add_reason_desc" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, product_code, product_name, plan_code, plan_name, pf_version, risk_group_code, 
        risk_group_name, version_no, define_source, duty_detail, ig_flag, valid_flag, 
        effect_time, expire_time, add_reason, add_reason_desc, created_by, sys_ctime, 
        updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE 1=1
        <if test="productCode != null and productCode != ''">
            AND product_code = #{productCode,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null and planCode != ''">
            AND plan_code = #{planCode,jdbcType=VARCHAR}
        </if>
        <if test="riskGroupCode != null and riskGroupCode != ''">
            AND risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        </if>
        <if test="validFlag != null and validFlag != ''">
            AND valid_flag = #{validFlag,jdbcType=VARCHAR}
        </if>
        <if test="igFlag != null and igFlag != ''">
            AND ig_flag = #{igFlag,jdbcType=VARCHAR}
        </if>
        <if test="versionNo != null">
            AND version_no = #{versionNo,jdbcType=INTEGER}
        </if>
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByProductCode" parameterType="java.lang.String" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByPlanCode" parameterType="java.lang.String" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE plan_code = #{planCode,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByRiskGroupCode" parameterType="java.lang.String" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByProductAndPlan" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        AND plan_code = #{planCode,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByProductPlanAndRisk" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE product_code = #{productCode,jdbcType=VARCHAR}
        AND plan_code = #{planCode,jdbcType=VARCHAR}
        AND risk_group_code = #{riskGroupCode,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByValidFlag" parameterType="java.lang.String" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE valid_flag = #{validFlag,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByIgFlag" parameterType="java.lang.String" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE ig_flag = #{igFlag,jdbcType=VARCHAR}
        ORDER BY version_no DESC, sys_ctime DESC
    </select>

    <select id="selectByVersionNo" parameterType="java.lang.Integer" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE version_no = #{versionNo,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByEffectTimeRange" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND effect_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND effect_time <= #{endTime}
        </if>
        ORDER BY effect_time DESC, version_no DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsClauseConfigMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_CONFIG
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_CLAUSE_CONFIG
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_CONFIG (
            product_code, product_name, plan_code, plan_name, pf_version, risk_group_code, 
            risk_group_name, version_no, define_source, duty_detail, ig_flag, valid_flag, 
            effect_time, expire_time, add_reason, add_reason_desc, created_by, sys_ctime, 
            updated_by, sys_utime
        ) VALUES (
            #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, 
            #{planName,jdbcType=VARCHAR}, #{pfVersion,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, 
            #{riskGroupName,jdbcType=VARCHAR}, #{versionNo,jdbcType=INTEGER}, #{defineSource,jdbcType=VARCHAR}, 
            #{dutyDetail,jdbcType=VARCHAR}, #{igFlag,jdbcType=VARCHAR}, #{validFlag,jdbcType=VARCHAR}, 
            #{effectTime,jdbcType=TIMESTAMP}, #{expireTime,jdbcType=TIMESTAMP}, #{addReason,jdbcType=VARCHAR}, 
            #{addReasonDesc,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, 
            #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCode != null">product_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="pfVersion != null">pf_version,</if>
            <if test="riskGroupCode != null">risk_group_code,</if>
            <if test="riskGroupName != null">risk_group_name,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="defineSource != null">define_source,</if>
            <if test="dutyDetail != null">duty_detail,</if>
            <if test="igFlag != null">ig_flag,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="effectTime != null">effect_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="addReason != null">add_reason,</if>
            <if test="addReasonDesc != null">add_reason_desc,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="planCode != null">#{planCode,jdbcType=VARCHAR},</if>
            <if test="planName != null">#{planName,jdbcType=VARCHAR},</if>
            <if test="pfVersion != null">#{pfVersion,jdbcType=VARCHAR},</if>
            <if test="riskGroupCode != null">#{riskGroupCode,jdbcType=VARCHAR},</if>
            <if test="riskGroupName != null">#{riskGroupName,jdbcType=VARCHAR},</if>
            <if test="versionNo != null">#{versionNo,jdbcType=INTEGER},</if>
            <if test="defineSource != null">#{defineSource,jdbcType=VARCHAR},</if>
            <if test="dutyDetail != null">#{dutyDetail,jdbcType=VARCHAR},</if>
            <if test="igFlag != null">#{igFlag,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">#{validFlag,jdbcType=VARCHAR},</if>
            <if test="effectTime != null">#{effectTime,jdbcType=TIMESTAMP},</if>
            <if test="expireTime != null">#{expireTime,jdbcType=TIMESTAMP},</if>
            <if test="addReason != null">#{addReason,jdbcType=VARCHAR},</if>
            <if test="addReasonDesc != null">#{addReasonDesc,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO">
        UPDATE CLMS_CLAUSE_CONFIG
        <set>
            <if test="productCode != null">product_code = #{productCode,jdbcType=VARCHAR},</if>
            <if test="productName != null">product_name = #{productName,jdbcType=VARCHAR},</if>
            <if test="planCode != null">plan_code = #{planCode,jdbcType=VARCHAR},</if>
            <if test="planName != null">plan_name = #{planName,jdbcType=VARCHAR},</if>
            <if test="pfVersion != null">pf_version = #{pfVersion,jdbcType=VARCHAR},</if>
            <if test="riskGroupCode != null">risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},</if>
            <if test="riskGroupName != null">risk_group_name = #{riskGroupName,jdbcType=VARCHAR},</if>
            <if test="versionNo != null">version_no = #{versionNo,jdbcType=INTEGER},</if>
            <if test="defineSource != null">define_source = #{defineSource,jdbcType=VARCHAR},</if>
            <if test="dutyDetail != null">duty_detail = #{dutyDetail,jdbcType=VARCHAR},</if>
            <if test="igFlag != null">ig_flag = #{igFlag,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">valid_flag = #{validFlag,jdbcType=VARCHAR},</if>
            <if test="effectTime != null">effect_time = #{effectTime,jdbcType=TIMESTAMP},</if>
            <if test="expireTime != null">expire_time = #{expireTime,jdbcType=TIMESTAMP},</if>
            <if test="addReason != null">add_reason = #{addReason,jdbcType=VARCHAR},</if>
            <if test="addReasonDesc != null">add_reason_desc = #{addReasonDesc,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO">
        UPDATE CLMS_CLAUSE_CONFIG
        SET product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            pf_version = #{pfVersion,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            version_no = #{versionNo,jdbcType=INTEGER},
            define_source = #{defineSource,jdbcType=VARCHAR},
            duty_detail = #{dutyDetail,jdbcType=VARCHAR},
            ig_flag = #{igFlag,jdbcType=VARCHAR},
            valid_flag = #{validFlag,jdbcType=VARCHAR},
            effect_time = #{effectTime,jdbcType=TIMESTAMP},
            expire_time = #{expireTime,jdbcType=TIMESTAMP},
            add_reason = #{addReason,jdbcType=VARCHAR},
            add_reason_desc = #{addReasonDesc,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
