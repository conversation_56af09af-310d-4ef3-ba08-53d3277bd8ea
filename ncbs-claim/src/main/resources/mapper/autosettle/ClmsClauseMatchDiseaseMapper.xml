<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseMatchDiseaseMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO" id="ClmsClauseMatchDiseaseMap">
        <id property="id" column="id" />
        <result property="configId" column="config_id" />
        <result property="matchId" column="match_id" />
        <result property="medicalDir1Id" column="medical_dir1_id" />
        <result property="medicalDir1Name" column="medical_dir1_name" />
        <result property="medicalDir2Id" column="medical_dir2_id" />
        <result property="medicalDir2Name" column="medical_dir2_name" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, config_id, match_id, medical_dir1_id, medical_dir1_name, medical_dir2_id, 
        medical_dir2_name, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE 1=1
        <if test="configId != null">
            AND config_id = #{configId,jdbcType=INTEGER}
        </if>
        <if test="matchId != null">
            AND match_id = #{matchId,jdbcType=INTEGER}
        </if>
        <if test="medicalDir1Id != null">
            AND medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER}
        </if>
        <if test="medicalDir2Id != null">
            AND medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER}
        </if>
        <if test="medicalDir1Name != null and medicalDir1Name != ''">
            AND medical_dir1_name LIKE CONCAT('%', #{medicalDir1Name}, '%')
        </if>
        <if test="medicalDir2Name != null and medicalDir2Name != ''">
            AND medical_dir2_name LIKE CONCAT('%', #{medicalDir2Name}, '%')
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByConfigId" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE config_id = #{configId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMatchId" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE match_id = #{matchId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDir1Id" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDir2Id" parameterType="java.lang.Integer" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDir1Name" parameterType="java.lang.String" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir1_name LIKE CONCAT('%', #{medicalDir1Name}, '%')
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDir2Name" parameterType="java.lang.String" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir2_name LIKE CONCAT('%', #{medicalDir2Name}, '%')
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByConfigIdAndMatchId" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE config_id = #{configId,jdbcType=INTEGER}
        AND match_id = #{matchId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDirIds" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER}
        <if test="medicalDir2Id != null">
            AND medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicalDir1Ids" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE medical_dir1_id IN
        <foreach collection="medicalDir1Ids" item="dirId" open="(" separator="," close=")">
            #{dirId}
        </foreach>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsClauseMatchDiseaseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_CLAUSE_MATCH_DISEASE
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_MATCH_DISEASE (
            config_id, match_id, medical_dir1_id, medical_dir1_name, medical_dir2_id, 
            medical_dir2_name, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{configId,jdbcType=INTEGER}, #{matchId,jdbcType=INTEGER}, #{medicalDir1Id,jdbcType=INTEGER}, 
            #{medicalDir1Name,jdbcType=VARCHAR}, #{medicalDir2Id,jdbcType=INTEGER}, #{medicalDir2Name,jdbcType=VARCHAR}, 
            #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
            #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_CLAUSE_MATCH_DISEASE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="matchId != null">match_id,</if>
            <if test="medicalDir1Id != null">medical_dir1_id,</if>
            <if test="medicalDir1Name != null">medical_dir1_name,</if>
            <if test="medicalDir2Id != null">medical_dir2_id,</if>
            <if test="medicalDir2Name != null">medical_dir2_name,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId,jdbcType=INTEGER},</if>
            <if test="matchId != null">#{matchId,jdbcType=INTEGER},</if>
            <if test="medicalDir1Id != null">#{medicalDir1Id,jdbcType=INTEGER},</if>
            <if test="medicalDir1Name != null">#{medicalDir1Name,jdbcType=VARCHAR},</if>
            <if test="medicalDir2Id != null">#{medicalDir2Id,jdbcType=INTEGER},</if>
            <if test="medicalDir2Name != null">#{medicalDir2Name,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO">
        UPDATE CLMS_CLAUSE_MATCH_DISEASE
        <set>
            <if test="configId != null">config_id = #{configId,jdbcType=INTEGER},</if>
            <if test="matchId != null">match_id = #{matchId,jdbcType=INTEGER},</if>
            <if test="medicalDir1Id != null">medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER},</if>
            <if test="medicalDir1Name != null">medical_dir1_name = #{medicalDir1Name,jdbcType=VARCHAR},</if>
            <if test="medicalDir2Id != null">medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER},</if>
            <if test="medicalDir2Name != null">medical_dir2_name = #{medicalDir2Name,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO">
        UPDATE CLMS_CLAUSE_MATCH_DISEASE
        SET config_id = #{configId,jdbcType=INTEGER},
            match_id = #{matchId,jdbcType=INTEGER},
            medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER},
            medical_dir1_name = #{medicalDir1Name,jdbcType=VARCHAR},
            medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER},
            medical_dir2_name = #{medicalDir2Name,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
