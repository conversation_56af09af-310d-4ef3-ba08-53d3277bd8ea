package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 条款匹责疾病范围表DTO
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@Setter
@Getter
@ApiModel("条款匹责疾病范围表")
public class ClmsClauseMatchDiseaseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("外键")
    private Integer configId;

    @ApiModelProperty("自动匹责id")
    private Integer matchId;

    @ApiModelProperty("一级医疗目录id")
    private Integer medicalDir1Id;

    @ApiModelProperty("一级医疗目录名称")
    private String medicalDir1Name;

    @ApiModelProperty("二级医疗目录id")
    private Integer medicalDir2Id;

    @ApiModelProperty("二级医疗目录名称")
    private String medicalDir2Name;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime;

    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    @ApiModelProperty("最新修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime;
}
