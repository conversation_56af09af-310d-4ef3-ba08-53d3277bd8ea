package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseTaskDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 流程任务表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsClauseTaskMapper extends BaseDao<ClmsClauseTaskDTO> {

    /**
     * 根据配置ID查询流程任务
     * 
     * @param configId 配置ID
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByConfigId(@Param("configId") Integer configId);

    /**
     * 根据上一个任务ID查询流程任务
     * 
     * @param flowInId 上一个任务ID
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByFlowInId(@Param("flowInId") Integer flowInId);

    /**
     * 根据任务节点查询流程任务
     * 
     * @param taskNode 任务节点
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByTaskNode(@Param("taskNode") String taskNode);

    /**
     * 根据节点状态查询流程任务
     * 
     * @param nodeStatus 节点状态
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByNodeStatus(@Param("nodeStatus") String nodeStatus);

    /**
     * 根据任务处理人查询流程任务
     * 
     * @param taskUserCode 任务处理人
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByTaskUserCode(@Param("taskUserCode") String taskUserCode);

    /**
     * 根据版本号查询流程任务
     * 
     * @param versionNo 版本号
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByVersionNo(@Param("versionNo") Integer versionNo);

    /**
     * 根据配置ID和任务节点查询流程任务
     * 
     * @param configId 配置ID
     * @param taskNode 任务节点
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByConfigIdAndTaskNode(@Param("configId") Integer configId, @Param("taskNode") String taskNode);

    /**
     * 根据配置ID和节点状态查询流程任务
     * 
     * @param configId 配置ID
     * @param nodeStatus 节点状态
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByConfigIdAndNodeStatus(@Param("configId") Integer configId, @Param("nodeStatus") String nodeStatus);

    /**
     * 根据流入时间范围查询流程任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByFlowInTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据流出时间范围查询流程任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByFlowOutTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据创建时间范围查询流程任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流程任务列表
     */
    List<ClmsClauseTaskDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
