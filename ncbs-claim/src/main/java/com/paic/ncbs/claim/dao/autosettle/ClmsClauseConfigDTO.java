package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 条款理赔配置表DTO
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@Setter
@Getter
@ApiModel("条款理赔配置表")
public class ClmsClauseConfigDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("险种代码")
    private String planCode;

    @ApiModelProperty("险种名称")
    private String planName;

    @ApiModelProperty("产品工厂的版本")
    private String pfVersion;

    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    @ApiModelProperty("方案名称")
    private String riskGroupName;

    @ApiModelProperty("版本号")
    private Integer versionNo;

    @ApiModelProperty("来源")
    private String defineSource;

    @ApiModelProperty("责任明细")
    private String dutyDetail;

    @ApiModelProperty("个团标记")
    private String igFlag;

    @ApiModelProperty("生效状态")
    private String validFlag;

    @ApiModelProperty("生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectTime;

    @ApiModelProperty("失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty("新增原因分类")
    private String addReason;

    @ApiModelProperty("新增原因描述")
    private String addReasonDesc;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime;

    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    @ApiModelProperty("最新修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime;
}
