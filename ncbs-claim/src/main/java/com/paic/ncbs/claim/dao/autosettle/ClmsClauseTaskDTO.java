package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 流程任务表DTO
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@Setter
@Getter
@ApiModel("流程任务表")
public class ClmsClauseTaskDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("上一个任务id")
    private Integer flowInId;

    @ApiModelProperty("理赔配置id")
    private Integer configId;

    @ApiModelProperty("版本号")
    private Integer versionNo;

    @ApiModelProperty("任务节点")
    private String taskNode;

    @ApiModelProperty("流入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flowInTime;

    @ApiModelProperty("开始处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("流出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flowOutTime;

    @ApiModelProperty("节点状态")
    private String nodeStatus;

    @ApiModelProperty("任务处理人")
    private String taskUserCode;

    @ApiModelProperty("审核意见")
    private String reviewOpinion;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime;

    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    @ApiModelProperty("最新修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime;
}
