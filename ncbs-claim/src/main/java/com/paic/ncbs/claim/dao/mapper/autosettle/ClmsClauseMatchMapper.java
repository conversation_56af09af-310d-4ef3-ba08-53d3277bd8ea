package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 条款匹责配置表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsClauseMatchMapper extends BaseDao<ClmsClauseMatchDTO> {

    /**
     * 根据配置ID查询条款匹责配置
     * 
     * @param configId 配置ID
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByConfigId(@Param("configId") Integer configId);

    /**
     * 根据产品代码查询条款匹责配置
     * 
     * @param productCode 产品代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByProductCode(@Param("productCode") String productCode);

    /**
     * 根据险种代码查询条款匹责配置
     * 
     * @param planCode 险种代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByPlanCode(@Param("planCode") String planCode);

    /**
     * 根据方案代码查询条款匹责配置
     * 
     * @param riskGroupCode 方案代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByRiskGroupCode(@Param("riskGroupCode") String riskGroupCode);

    /**
     * 根据责任代码查询条款匹责配置
     * 
     * @param dutyCode 责任代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByDutyCode(@Param("dutyCode") String dutyCode);

    /**
     * 根据责任明细代码查询条款匹责配置
     * 
     * @param dutyDetailCode 责任明细代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByDutyDetailCode(@Param("dutyDetailCode") String dutyDetailCode);

    /**
     * 根据发票类型查询条款匹责配置
     * 
     * @param billType 发票类型
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByBillType(@Param("billType") String billType);

    /**
     * 根据产品代码和险种代码查询条款匹责配置
     * 
     * @param productCode 产品代码
     * @param planCode 险种代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByProductAndPlan(@Param("productCode") String productCode, @Param("planCode") String planCode);

    /**
     * 根据产品代码、险种代码和方案代码查询条款匹责配置
     * 
     * @param productCode 产品代码
     * @param planCode 险种代码
     * @param riskGroupCode 方案代码
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByProductPlanAndRisk(@Param("productCode") String productCode, 
                                                        @Param("planCode") String planCode, 
                                                        @Param("riskGroupCode") String riskGroupCode);

    /**
     * 根据配置ID查询条款匹责配置（按匹配顺序排序）
     * 
     * @param configId 配置ID
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByConfigIdOrderBySequence(@Param("configId") Integer configId);

    /**
     * 根据关键字模糊查询条款匹责配置
     * 
     * @param matchKeys 关键字
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByMatchKeys(@Param("matchKeys") String matchKeys);

    /**
     * 根据创建时间范围查询条款匹责配置
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 条款匹责配置列表
     */
    List<ClmsClauseMatchDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
