package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDirDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 医疗ICD目录关系表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsMedicalIcdDirMapper extends BaseDao<ClmsMedicalIcdDirDTO> {

    /**
     * 根据ICD编码查询医疗ICD目录关系
     * 
     * @param dianoseCode ICD编码
     * @return 医疗ICD目录关系列表
     */
    List<ClmsMedicalIcdDirDTO> selectByDianoseCode(@Param("dianoseCode") String dianoseCode);

    /**
     * 根据目录ID查询医疗ICD目录关系
     * 
     * @param dirId 目录ID
     * @return 医疗ICD目录关系列表
     */
    List<ClmsMedicalIcdDirDTO> selectByDirId(@Param("dirId") Integer dirId);

    /**
     * 根据ICD编码列表查询医疗ICD目录关系
     * 
     * @param dianoseCodes ICD编码列表
     * @return 医疗ICD目录关系列表
     */
    List<ClmsMedicalIcdDirDTO> selectByDianoseCodes(@Param("dianoseCodes") List<String> dianoseCodes);

    /**
     * 根据目录ID列表查询医疗ICD目录关系
     * 
     * @param dirIds 目录ID列表
     * @return 医疗ICD目录关系列表
     */
    List<ClmsMedicalIcdDirDTO> selectByDirIds(@Param("dirIds") List<Integer> dirIds);

    /**
     * 根据创建时间范围查询医疗ICD目录关系
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 医疗ICD目录关系列表
     */
    List<ClmsMedicalIcdDirDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
