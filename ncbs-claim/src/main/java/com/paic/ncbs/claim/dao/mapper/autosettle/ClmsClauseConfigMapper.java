package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfigDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 条款理赔配置表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsClauseConfigMapper extends BaseDao<ClmsClauseConfigDTO> {

    /**
     * 根据产品代码查询条款理赔配置
     * 
     * @param productCode 产品代码
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByProductCode(@Param("productCode") String productCode);

    /**
     * 根据险种代码查询条款理赔配置
     * 
     * @param planCode 险种代码
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByPlanCode(@Param("planCode") String planCode);

    /**
     * 根据方案代码查询条款理赔配置
     * 
     * @param riskGroupCode 方案代码
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByRiskGroupCode(@Param("riskGroupCode") String riskGroupCode);

    /**
     * 根据产品代码和险种代码查询条款理赔配置
     * 
     * @param productCode 产品代码
     * @param planCode 险种代码
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByProductAndPlan(@Param("productCode") String productCode, @Param("planCode") String planCode);

    /**
     * 根据产品代码、险种代码和方案代码查询条款理赔配置
     * 
     * @param productCode 产品代码
     * @param planCode 险种代码
     * @param riskGroupCode 方案代码
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByProductPlanAndRisk(@Param("productCode") String productCode, 
                                                         @Param("planCode") String planCode, 
                                                         @Param("riskGroupCode") String riskGroupCode);

    /**
     * 根据有效标志查询条款理赔配置
     * 
     * @param validFlag 有效标志
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByValidFlag(@Param("validFlag") String validFlag);

    /**
     * 根据个团标记查询条款理赔配置
     * 
     * @param igFlag 个团标记
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByIgFlag(@Param("igFlag") String igFlag);

    /**
     * 根据版本号查询条款理赔配置
     * 
     * @param versionNo 版本号
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByVersionNo(@Param("versionNo") Integer versionNo);

    /**
     * 根据生效时间范围查询条款理赔配置
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByEffectTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据创建时间范围查询条款理赔配置
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 条款理赔配置列表
     */
    List<ClmsClauseConfigDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
