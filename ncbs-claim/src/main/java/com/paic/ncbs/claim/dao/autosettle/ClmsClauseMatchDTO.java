package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 条款匹责配置表DTO
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@Setter
@Getter
@ApiModel("条款匹责配置表")
public class ClmsClauseMatchDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("外键")
    private Integer configId;

    @ApiModelProperty("自动匹配顺序")
    private Integer matchSequece;

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("险种代码")
    private String planCode;

    @ApiModelProperty("险种名称")
    private String planName;

    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    @ApiModelProperty("方案名称")
    private String riskGroupName;

    @ApiModelProperty("责任代码")
    private String dutyCode;

    @ApiModelProperty("责任名称")
    private String dutyName;

    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    @ApiModelProperty("发票类型")
    private String billType;

    @ApiModelProperty("票据明细包含的关键字")
    private String matchKeys;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime;

    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    @ApiModelProperty("最新修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime;
}
