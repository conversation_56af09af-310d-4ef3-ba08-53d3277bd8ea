package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDiseaseDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 条款匹责疾病范围表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsClauseMatchDiseaseMapper extends BaseDao<ClmsClauseMatchDiseaseDTO> {

    /**
     * 根据配置ID查询条款匹责疾病范围
     * 
     * @param configId 配置ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByConfigId(@Param("configId") Integer configId);

    /**
     * 根据匹责ID查询条款匹责疾病范围
     * 
     * @param matchId 匹责ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMatchId(@Param("matchId") Integer matchId);

    /**
     * 根据一级医疗目录ID查询条款匹责疾病范围
     * 
     * @param medicalDir1Id 一级医疗目录ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDir1Id(@Param("medicalDir1Id") Integer medicalDir1Id);

    /**
     * 根据二级医疗目录ID查询条款匹责疾病范围
     * 
     * @param medicalDir2Id 二级医疗目录ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDir2Id(@Param("medicalDir2Id") Integer medicalDir2Id);

    /**
     * 根据一级医疗目录名称查询条款匹责疾病范围
     * 
     * @param medicalDir1Name 一级医疗目录名称
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDir1Name(@Param("medicalDir1Name") String medicalDir1Name);

    /**
     * 根据二级医疗目录名称查询条款匹责疾病范围
     * 
     * @param medicalDir2Name 二级医疗目录名称
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDir2Name(@Param("medicalDir2Name") String medicalDir2Name);

    /**
     * 根据配置ID和匹责ID查询条款匹责疾病范围
     * 
     * @param configId 配置ID
     * @param matchId 匹责ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByConfigIdAndMatchId(@Param("configId") Integer configId, @Param("matchId") Integer matchId);

    /**
     * 根据一级和二级医疗目录ID查询条款匹责疾病范围
     * 
     * @param medicalDir1Id 一级医疗目录ID
     * @param medicalDir2Id 二级医疗目录ID
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDirIds(@Param("medicalDir1Id") Integer medicalDir1Id, @Param("medicalDir2Id") Integer medicalDir2Id);

    /**
     * 根据医疗目录ID列表查询条款匹责疾病范围
     * 
     * @param medicalDir1Ids 一级医疗目录ID列表
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByMedicalDir1Ids(@Param("medicalDir1Ids") List<Integer> medicalDir1Ids);

    /**
     * 根据创建时间范围查询条款匹责疾病范围
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 条款匹责疾病范围列表
     */
    List<ClmsClauseMatchDiseaseDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
