package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 发票匹责结果表DTO
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@Setter
@Getter
@ApiModel("发票匹责结果表")
public class ClmsInvoiceMatchDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("案件号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("匹责类型")
    private String matchType;

    @ApiModelProperty("自动匹责的配置项id-自动匹责时填充")
    private Integer configId;

    @ApiModelProperty("发票账单信息表主键")
    private String idAhcsInvoiceInfo;

    @ApiModelProperty("执行成功标志")
    private Integer matchSign;

    @ApiModelProperty("失败描述")
    private String failDesc;

    @ApiModelProperty("匹责结论")
    private String matchResult;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime;

    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    @ApiModelProperty("最新修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime;
}
