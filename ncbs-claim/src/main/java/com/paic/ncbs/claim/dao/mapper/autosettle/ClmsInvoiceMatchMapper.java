package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatchDTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 发票匹责结果表Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-09-26
 */
@MapperScan
public interface ClmsInvoiceMatchMapper extends BaseDao<ClmsInvoiceMatchDTO> {

    /**
     * 根据案件号查询发票匹责结果
     * 
     * @param reportNo 案件号
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据配置ID查询发票匹责结果
     * 
     * @param configId 配置ID
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByConfigId(@Param("configId") Integer configId);

    /**
     * 根据匹责类型查询发票匹责结果
     * 
     * @param matchType 匹责类型
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByMatchType(@Param("matchType") String matchType);

    /**
     * 根据执行成功标志查询发票匹责结果
     * 
     * @param matchSign 执行成功标志
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByMatchSign(@Param("matchSign") Integer matchSign);

    /**
     * 根据匹责结论查询发票匹责结果
     * 
     * @param matchResult 匹责结论
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByMatchResult(@Param("matchResult") String matchResult);

    /**
     * 根据发票账单信息表主键查询发票匹责结果
     * 
     * @param idAhcsInvoiceInfo 发票账单信息表主键
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByIdAhcsInvoiceInfo(@Param("idAhcsInvoiceInfo") String idAhcsInvoiceInfo);

    /**
     * 根据案件号和赔付次数查询发票匹责结果
     * 
     * @param reportNo 案件号
     * @param caseTimes 赔付次数
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 根据案件号和匹责类型查询发票匹责结果
     * 
     * @param reportNo 案件号
     * @param matchType 匹责类型
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByReportNoAndMatchType(@Param("reportNo") String reportNo, @Param("matchType") String matchType);

    /**
     * 根据案件号和执行成功标志查询发票匹责结果
     * 
     * @param reportNo 案件号
     * @param matchSign 执行成功标志
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByReportNoAndMatchSign(@Param("reportNo") String reportNo, @Param("matchSign") Integer matchSign);

    /**
     * 查询匹责失败的发票记录
     * 
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectFailedMatches();

    /**
     * 查询匹责成功的发票记录
     * 
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectSuccessMatches();

    /**
     * 根据创建时间范围查询发票匹责结果
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发票匹责结果列表
     */
    List<ClmsInvoiceMatchDTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
