package com.paic.ncbs.claim.controller.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.RelationshipWithApplicantType;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyHolderMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.report.BankInfoDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentBackSubmitParamVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.report.HistoryCaseVO;
import com.paic.ncbs.claim.model.vo.report.OcasRealNameVo;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.settle.CustomerService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "历史案件")
@RestController
@RequestMapping("/report")
public class QueryReportController {

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ReportInfoService reportInfoService;

    @Autowired
    private PaymentInfoService paymentInfoService;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private CommonParameterMapper commonParameterMapper ;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;
    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;
    @Autowired
    private AhcsPolicyHolderMapper ahcsPolicyHolderMapper;

    @GetMapping(value = "/getPaymentInfoList")
    @ApiOperation(value = "查询支付对象信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "paymentInfoType", value = "支付类型", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "subTimes", value = "预赔次数", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query"),
            @ApiImplicitParam(name = "paymentUsage", value = "支付使用率", required = true, dataType = "String", dataTypeClass=String.class ,paramType = "Query"),
            @ApiImplicitParam(name = "isTemp", value = "是否模板", required = true, dataType = "String", dataTypeClass=String.class, paramType = "Query")
    })
    public ResponseResult<List<PaymentItemComData>> getPaymentInfoList(String reportNo, String caseTimes,
                                                                       String paymentInfoType, String subTimes, String paymentUsage, String isTemp) {
        LogUtil.info("获取支付信息列表入参:reportNo={},caseTimes={},isTemp={}", reportNo, caseTimes, isTemp);
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        RapeCheckUtil.checkParamEmpty(caseTimes, "赔付次数");
        PaymentInfoDTO param = new PaymentInfoDTO();
        param.setReportNo(reportNo);
        param.setCaseTimes(Integer.valueOf(caseTimes));
        param.setPaymentUsage(paymentUsage);
        param.setPaymentInfoType(paymentInfoType);
        return ResponseResult.success(paymentInfoService.getPaymentInfoList(param));
    }

    @GetMapping(value = "/getHistoryCaseListByCertificateNo")
    @ApiOperation(value = "根据客户证件号、姓名查询历史案件")
    public ResponseResult<Map<String, Object>> getHistoryCaseListByCertificateNo(@ApiParam("证件号") String certificateNo, @ApiParam("姓名") String name,Pager pager) {
        RapeCheckUtil.checkParamEmpty(certificateNo, "证件号");
        RapeCheckUtil.checkParamEmpty(name, "姓名");
        LogUtil.info("根据客户号查询历史案件列表入参certificateNo:{}", certificateNo);
        return getMapResponseResult(certificateNo, name, pager);
    }

    public ResponseResult<Map<String, Object>> getMapResponseResult(@ApiParam("证件号") String certificateNo, @ApiParam("姓名") String name, Pager pager) {
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy(" reportDate desc");
        List<HistoryCaseVO> result  = new ArrayList<>() ;
        List<HistoryCaseDTO> list = reportInfoService.getHistoryCaseByCertificateNo(certificateNo, name);
        List<HistoryCaseVO> historyCaseList = transformVO(list);
        for (HistoryCaseVO historyCaseVO : historyCaseList) {
            String reportNo = historyCaseVO.getReportNo();
            Short caseTimes = historyCaseVO.getCaseTimes();
            //赔款
            BigDecimal sumPayFee = policyPayService.getSumPayFee(reportNo, Integer.valueOf(caseTimes));
            if (BigDecimal.ZERO.compareTo(sumPayFee) == 0) {
                historyCaseVO.setPolicySumPay(null);
            } else {
                historyCaseVO.setPolicySumPay(sumPayFee);
            }
            //流程状态c
            String processStatusName = caseProcessService.getCaseProcessStatusName(reportNo, Integer.valueOf(caseTimes));
            historyCaseVO.setProcessStatusName(processStatusName);
            //未决金额待定
            List<ClmsEstimateRecord> records = clmsEstimateRecordService.getRecordByReportNoAndType(reportNo, String.valueOf(caseTimes), null);
            if(ListUtils.isNotEmpty(records)){
                historyCaseVO.setPendingAmount(String.valueOf(records.get(0).getEstimateAmount()));
            }
            BigDecimal changeAmt = estimateChangeMapper.getEstimateChangeAmount(reportNo,Integer.valueOf(caseTimes));
            if(changeAmt != null){
                //有修正金额，替换为修正金额
                historyCaseVO.setPendingAmount(changeAmt.toString());
            }

            if (!"已结案".equals(processStatusName) ) {
                historyCaseVO.setPolicySumPay(null);
            }
        }
        List<HistoryCaseVO> endCaseData=new ArrayList<>() ;
        List<HistoryCaseVO> notEndCaseData=new ArrayList<>() ;
        for( HistoryCaseVO historyCaseDTO :historyCaseList){
            if ("已结案".equals(historyCaseDTO.getCaseStatusName())){
                endCaseData.add(historyCaseDTO);
            }else{
                notEndCaseData.add(historyCaseDTO);
            }
        }
        endCaseData.stream().sorted((x1,x2)->{
            return  x1.getReportDate().after(x2.getReportDate())  ? 1:0 ;
        });
        notEndCaseData.stream().sorted((x1,x2)->{
            return  x1.getReportDate().after(x2.getReportDate())  ? 1:0 ;
        });
        result.addAll(endCaseData);
        result.addAll(notEndCaseData);

        PageInfo<HistoryCaseDTO> pageInfo = new PageInfo<>(list);
        pager.setTotalRows((int) pageInfo.getTotal());
        LogUtil.info("根据客户号查询历史案件列表出参historyCaseList:{}", JSON.toJSONString(historyCaseList));
        PageMethod.clearPage();
        return ResponseResult.success(result, pager);
    }

    @GetMapping(value = "/getCustomerInfoByReportNo")
    @ApiOperation(value = "根据报案号查询客户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号", required = true, dataType = "String", dataTypeClass=String.class,paramType = "Query")
    })
    public ResponseResult<ReportCustomerInfoVO> getCustomerInfoByReportNo(String reportNo) {
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(reportNo);
        ReportCustomerInfoVO reportCustomerInfoVO = new ReportCustomerInfoVO();
        LogUtil.info("客户信息customerInfo:{}", JSON.toJSONString(customerInfo));
        if (customerInfo == null) {
            return ResponseResult.success(reportCustomerInfoVO);
        }
        List<String> persion = ahcsInsuredPresonMapper.getAhcsInsuredPersionByClientNoAndReportNo(customerInfo.getClientNo(), reportNo);
        BeanUtils.copyProperties(customerInfo, reportCustomerInfoVO);
        //  是否社保: 1为社保， 0或空为非社保
        if (CollectionUtils.isEmpty(persion)) {
            reportCustomerInfoVO.setIsSociaSecurity(null);
        } else {
            reportCustomerInfoVO.setIsSociaSecurity(persion.get(0));
        }
        reportCustomerInfoVO.setClientCertificateType(customerInfo.getCertificateType());
        reportCustomerInfoVO.setCertificateTypeName(CertificateTypeEnum.getName(customerInfo.getCertificateType()));
        reportCustomerInfoVO.setPersonnelAttribute(customerInfo.getClientCluster());
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        // 报案来电号码
        String phoneNo = reportInfo.getReporterCallNo();
        reportCustomerInfoVO.setReporterCallNo(phoneNo);
        //投保人相关信息
        List<AhcsPolicyHolderEntity> holderList = ahcsPolicyHolderMapper.getInfoByReportNo(reportNo);
        if(!holderList.isEmpty()){
            reportCustomerInfoVO.setApplicantName(holderList.get(0).getName());
            reportCustomerInfoVO.setApplicantCertType(holderList.get(0).getCertificateType());
            reportCustomerInfoVO.setApplicantCertNo(holderList.get(0).getCertificateNo());
        }
        //被保人与投保人关系
        String relationshipWithApplicant = ahcsInsuredPresonMapper.getRelToInsuredByReportNo(reportNo);
        reportCustomerInfoVO.setRelationshipWithApplicant(RelationshipWithApplicantType.getCode(relationshipWithApplicant));
        LogUtil.info("客户信息reportCustomerInfoVO:{}", JSON.toJSONString(reportCustomerInfoVO));
        return ResponseResult.success(reportCustomerInfoVO);
    }

    private List<HistoryCaseVO> transformVO(List<HistoryCaseDTO> historyCaseDTOs) {
        List<HistoryCaseVO> historyCaseList = new ArrayList<>();
        if (RapeCheckUtil.isListNotEmpty(historyCaseDTOs)) {
            for (HistoryCaseDTO dto : historyCaseDTOs) {
                HistoryCaseVO historyCaseVO = new HistoryCaseVO();
                BeanUtils.copyProperties(dto, historyCaseVO);
                CaseProcessDTO caseProcess = new CaseProcessDTO();
                caseProcess.setReportNo(dto.getReportNo());
                caseProcess.setCaseTimes(Integer.valueOf(dto.getCaseTimes()));
                CaseProcessDTO caseProcessDTO = caseProcessService.getProcessStatusAndEndAmount(caseProcess);
                historyCaseVO.setCaseStatusName(caseProcessDTO.getProcessStatusName());
                historyCaseVO.setEndCaseAmount(caseProcessDTO.getEndCaseAmount());
                historyCaseVO.setEstimateAmount(caseProcessDTO.getEstimateAmount());
                historyCaseVO.setEndCaseDate(caseProcessDTO.getEndCaseDate());
                historyCaseVO.setDepartmentCode(caseProcessDTO.getDepartmentCode());
                historyCaseVO.setDepartmentAbbrName(caseProcessDTO.getDepartmentAbbrName());
                if(dto.getPersonnelAttribute() != null){
                    historyCaseVO.setPersonnelAttribute(dto.getPersonnelAttribute());
                }
                historyCaseList.add(historyCaseVO);
            }
        }
        return historyCaseList;
    }

    @PostMapping(value = "/submitPaymentBack", consumes = "application/json")
    public ResponseResult<Object> submitPaymentBack(@RequestBody PaymentBackSubmitParamVO paymentBackSubmitParams) {
        List<PaymentInfoVO> list = paymentBackSubmitParams.getPaymentInfoVOList();
        if (list == null || list.size() < 1) {
            return ResponseResult.success();
        }
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        BeanUtils.copyProperties(list.get(0), paymentInfoDTO);
        paymentInfoService.updatePaymentInfo(paymentInfoDTO);
        return ResponseResult.success();
    }

    @GetMapping(value = "/getBankTypeList")
    public ResponseResult<JSONObject> getBankTypeList(String reportNo, String caseTimes) {
        List<CommonParameterTinyDTO> payBankList = commonParameterMapper.getCommonParameterList(new String[] {"BANK_TYPE"});
        if (ListUtils.isNotEmpty(payBankList)) {
            List<JSONObject> payBankTypeList = new ArrayList<>();
            payBankList.forEach(e -> {
                JSONObject js = new JSONObject();
                js.put("BANK_TYPE_CODE", e.getValueCode());
                js.put("BANK_TYPE_NAME", e.getValueChineseName());
                payBankTypeList.add(js);
            });
            JSONObject jo = new JSONObject();
            jo.put("BANK_TYPE_LIST", payBankTypeList);
            return ResponseResult.success(jo);
        }
        return ResponseResult.success(new JSONObject());
    }

    @GetMapping(value = "/getHistoryCaseList")
    @ApiOperation(value = "根据报案号.赔付次数被保人所有历史案件")
    public ResponseResult<Map<String, Object>> getHistoryCaseList(@ApiParam("报案人") String reportNo, @ApiParam("赔付次数") Integer caseTimes, Pager pager) {
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        if (null != customerInfo) {
            String name = customerInfo.getName();
            String certificateNo = customerInfo.getCertificateNo();
            return getMapResponseResult(certificateNo, name, pager);
        }
        return ResponseResult.success(new ArrayList<HistoryCaseVO>(), pager);
    }

    @GetMapping(value = "/getHistoryCaseListNew")
    @ApiOperation(value = "根据报案号.赔付次数被保人所有历史案件")
    public ResponseResult<Map<String, Object>> getHistoryCaseListNew(@ApiParam("报案号") String reportNo, @ApiParam("赔付次数") Integer caseTimes, Pager pager) {
        List<ClaimInfoToESVO> claimInfoToESVOs = reportCustomerInfoService.getHistoryCaseListNew(reportNo,caseTimes,pager);
        return ResponseResult.success(claimInfoToESVOs, pager);
    }

    /**
     * 获取真实被保人列表
     * @param reportNo
     * @return
     */
    @GetMapping(value = "/getCustomerInfoList")
    public ResponseResult<List<OcasInsuredDTO>> getCustomerInfoList(@RequestParam("reportNo") String reportNo) {
        RapeCheckUtil.checkParamEmpty(reportNo, "报案号");
        List<String> policyNoList = policyInfoMapper.getPolicyNo(reportNo);
        if (ListUtils.isEmptyList(policyNoList)) {
            return ResponseResult.success(new ArrayList<>());
        }
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(reportNo);
        if (customerInfo == null) {
            return ResponseResult.success(new ArrayList<>());
        }
        List<OcasInsuredDTO> insuredDTOList = ocasMapper.getRiskPersonnalList(policyNoList);
        List<OcasInsuredDTO> riskPersonnalListByName = ocasMapper.getRiskPersonnalListByName(policyNoList, null);
        if(ListUtils.isNotEmpty(riskPersonnalListByName)){
            insuredDTOList.addAll(riskPersonnalListByName);
        }
        if(ListUtils.isEmptyList(insuredDTOList)){
            return ResponseResult.success(new ArrayList<>());
        }
        LogUtil.audit("保单全量被保人reportNo={},list={}",reportNo,JSON.toJSONString(insuredDTOList));
        //根据保单号分组，姓名+证件号出现在所有保单中才返回
        Map<String,List<OcasInsuredDTO>> map = insuredDTOList.stream().collect(Collectors.groupingBy(OcasInsuredDTO::getPolicyNo));
        List<List<OcasInsuredDTO>> group = new ArrayList<>(map.values());
        List<OcasInsuredDTO> result = group.stream().reduce((list1,list2) -> {
            list1.retainAll(list2);
            return list1;
        }).filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        .sorted(Comparator.comparing(OcasInsuredDTO::getPersonnelAttribute).reversed()
                                .thenComparing(OcasInsuredDTO:: getInsuredName))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        return ResponseResult.success(result);
    }

    @ApiOperation(value = "银行信息查询")
    @GetMapping(value = "/getBankInfoList")
    public ResponseResult<List<BankInfoDTO>> getBankInfoList(@RequestParam("bankName") String bankName){
        LogUtil.info("QueryReportController getBankInfoList bankName = {}", bankName);
        List<BankInfoDTO> result = new ArrayList<>();
        //判断入参是否为空，为空则查询银行大类信息，不为空则根据入参查询对应的开户行信息
        if(StringUtils.isNotEmpty(bankName)){
            result = commonParameterMapper.getBranchBankInfoList(bankName);
        }else{
            result = commonParameterMapper.getBankInfoList();
        }
        return ResponseResult.success(result);
    }

    @ApiOperation(value = "自动实名化")
    @PostMapping(value = "/autoRealName")
    public ResponseResult autoRealName(@RequestBody OcasRealNameVo ocasRealNameVo){
        reportCustomerInfoService.autoRealName(ocasRealNameVo);
        return ResponseResult.success();
    }

}