#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成autosettle模块的Mapper、DTO和XML文件
基于create_auto_settle_table.sql中的表结构生成对应的Java代码

使用方法：
python generate_autosettle_code.py

生成的文件将保存到：
- DTO: ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/
- Mapper: ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/
- XML: ncbs-claim/src/main/resources/mapper/autosettle/
"""

import os
import re
from datetime import datetime

# 表结构定义 - 包含所有35个表的完整定义
TABLES = [
    {
        'table_name': 'CLMS_CLAUSE_TASK',
        'class_name': 'ClmsClauseTask',
        'comment': '流程任务表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'flowInId', 'type': 'Integer', 'column': 'flow_in_id', 'comment': '上一个任务id'},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '理赔配置id'},
            {'name': 'versionNo', 'type': 'Integer', 'column': 'version_no', 'comment': '版本号'},
            {'name': 'taskNode', 'type': 'String', 'column': 'task_node', 'comment': '任务节点'},
            {'name': 'flowInTime', 'type': 'Date', 'column': 'flow_in_time', 'comment': '流入时间'},
            {'name': 'startTime', 'type': 'Date', 'column': 'start_time', 'comment': '开始处理时间'},
            {'name': 'flowOutTime', 'type': 'Date', 'column': 'flow_out_time', 'comment': '流出时间'},
            {'name': 'nodeStatus', 'type': 'String', 'column': 'node_status', 'comment': '节点状态'},
            {'name': 'taskUserCode', 'type': 'String', 'column': 'task_user_code', 'comment': '任务处理人'},
            {'name': 'reviewOpinion', 'type': 'String', 'column': 'review_opinion', 'comment': '审核意见'},
            {'name': 'remark', 'type': 'String', 'column': 'remark', 'comment': '备注'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_CLAUSE_MATCH',
        'class_name': 'ClmsClauseMatch',
        'comment': '条款匹责配置表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'matchSequece', 'type': 'Integer', 'column': 'match_sequece', 'comment': '自动匹配顺序'},
            {'name': 'productCode', 'type': 'String', 'column': 'product_code', 'comment': '产品代码'},
            {'name': 'productName', 'type': 'String', 'column': 'product_name', 'comment': '产品名称'},
            {'name': 'planCode', 'type': 'String', 'column': 'plan_code', 'comment': '险种代码'},
            {'name': 'planName', 'type': 'String', 'column': 'plan_name', 'comment': '险种名称'},
            {'name': 'riskGroupCode', 'type': 'String', 'column': 'risk_group_code', 'comment': '方案代码'},
            {'name': 'riskGroupName', 'type': 'String', 'column': 'risk_group_name', 'comment': '方案名称'},
            {'name': 'dutyCode', 'type': 'String', 'column': 'duty_code', 'comment': '责任代码'},
            {'name': 'dutyName', 'type': 'String', 'column': 'duty_name', 'comment': '责任名称'},
            {'name': 'dutyDetailCode', 'type': 'String', 'column': 'duty_detail_code', 'comment': '责任明细代码'},
            {'name': 'dutyDetailName', 'type': 'String', 'column': 'duty_detail_name', 'comment': '责任明细名称'},
            {'name': 'billType', 'type': 'String', 'column': 'bill_type', 'comment': '发票类型'},
            {'name': 'matchKeys', 'type': 'String', 'column': 'match_keys', 'comment': '票据明细包含的关键字'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_CLAUSE_MATCH_DISEASE',
        'class_name': 'ClmsClauseMatchDisease',
        'comment': '条款匹责疾病范围表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'matchId', 'type': 'Integer', 'column': 'match_id', 'comment': '自动匹责id'},
            {'name': 'medicalDir1Id', 'type': 'Integer', 'column': 'medical_dir1_id', 'comment': '一级医疗目录id'},
            {'name': 'medicalDir1Name', 'type': 'String', 'column': 'medical_dir1_name', 'comment': '一级医疗目录名称'},
            {'name': 'medicalDir2Id', 'type': 'Integer', 'column': 'medical_dir2_id', 'comment': '二级医疗目录id'},
            {'name': 'medicalDir2Name', 'type': 'String', 'column': 'medical_dir2_name', 'comment': '二级医疗目录名称'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_INVOICE_MATCH',
        'class_name': 'ClmsInvoiceMatch',
        'comment': '发票匹责结果表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'reportNo', 'type': 'String', 'column': 'report_no', 'comment': '案件号'},
            {'name': 'caseTimes', 'type': 'Integer', 'column': 'case_times', 'comment': '赔付次数'},
            {'name': 'matchType', 'type': 'String', 'column': 'match_type', 'comment': '匹责类型'},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '自动匹责的配置项id-自动匹责时填充'},
            {'name': 'idAhcsInvoiceInfo', 'type': 'String', 'column': 'id_ahcs_invoice_info', 'comment': '发票账单信息表主键'},
            {'name': 'matchSign', 'type': 'Integer', 'column': 'match_sign', 'comment': '执行成功标志'},
            {'name': 'failDesc', 'type': 'String', 'column': 'fail_desc', 'comment': '失败描述'},
            {'name': 'matchResult', 'type': 'String', 'column': 'match_result', 'comment': '匹责结论'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_WAIT_PERIOD',
        'class_name': 'ClmsVerifyWaitPeriod',
        'comment': '条款核责配置-等待期表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'productCode', 'type': 'String', 'column': 'product_code', 'comment': '产品代码'},
            {'name': 'productName', 'type': 'String', 'column': 'product_name', 'comment': '产品名称'},
            {'name': 'planCode', 'type': 'String', 'column': 'plan_code', 'comment': '险种代码'},
            {'name': 'planName', 'type': 'String', 'column': 'plan_name', 'comment': '险种名称'},
            {'name': 'riskGroupCode', 'type': 'String', 'column': 'risk_group_code', 'comment': '方案代码'},
            {'name': 'riskGroupName', 'type': 'String', 'column': 'risk_group_name', 'comment': '方案名称'},
            {'name': 'diseaseWaitPeriod', 'type': 'Integer', 'column': 'disease_wait_period', 'comment': '新保疾病等待期（天）'},
            {'name': 'accidentWaitPeriod', 'type': 'Integer', 'column': 'accident_wait_period', 'comment': '新保意外等待期（天）'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_HOSPITAL',
        'class_name': 'ClmsVerifyHospital',
        'comment': '条款核责配置-医院范围表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'verifyLevel', 'type': 'Integer', 'column': 'verify_level', 'comment': '核责等级'},
            {'name': 'productCode', 'type': 'String', 'column': 'product_code', 'comment': '产品代码'},
            {'name': 'productName', 'type': 'String', 'column': 'product_name', 'comment': '产品名称'},
            {'name': 'planCode', 'type': 'String', 'column': 'plan_code', 'comment': '险种代码'},
            {'name': 'planName', 'type': 'String', 'column': 'plan_name', 'comment': '险种名称'},
            {'name': 'riskGroupCode', 'type': 'String', 'column': 'risk_group_code', 'comment': '方案代码'},
            {'name': 'riskGroupName', 'type': 'String', 'column': 'risk_group_name', 'comment': '方案名称'},
            {'name': 'dutyCode', 'type': 'String', 'column': 'duty_code', 'comment': '责任代码'},
            {'name': 'dutyName', 'type': 'String', 'column': 'duty_name', 'comment': '责任名称'},
            {'name': 'dutyDetailCode', 'type': 'String', 'column': 'duty_detail_code', 'comment': '责任明细代码'},
            {'name': 'dutyDetailName', 'type': 'String', 'column': 'duty_detail_name', 'comment': '责任明细名称'},
            {'name': 'hospitalLevel', 'type': 'String', 'column': 'hospital_level', 'comment': '医院等级'},
            {'name': 'hospitalGrade', 'type': 'String', 'column': 'hospital_grade', 'comment': '医院级别'},
            {'name': 'hospitalNature', 'type': 'String', 'column': 'hospital_nature', 'comment': '医院性质'},
            {'name': 'medicalConsultation', 'type': 'String', 'column': 'medical_consultation', 'comment': '就诊类型'},
            {'name': 'hospitalRegion', 'type': 'String', 'column': 'hospital_region', 'comment': '医院区域类型'},
            {'name': 'exceptRegion', 'type': 'String', 'column': 'except_region', 'comment': '除外省市'},
            {'name': 'includePackageId', 'type': 'Integer', 'column': 'include_package_id', 'comment': '协议扩展-医院包ID'},
            {'name': 'excludePackageId', 'type': 'Integer', 'column': 'exclude_package_id', 'comment': '除外-医院包ID'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_SPECIFIC_HOSPITAL',
        'class_name': 'ClmsVerifySpecificHospital',
        'comment': '条款核责配置-特定医院表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'vHospitalId', 'type': 'Integer', 'column': 'v_hospital_id', 'comment': '医院核责规则id'},
            {'name': 'specificType', 'type': 'String', 'column': 'specific_type', 'comment': '特定类型'},
            {'name': 'hospitalCode', 'type': 'String', 'column': 'hospital_code', 'comment': '医院代码'},
            {'name': 'hospitalName', 'type': 'String', 'column': 'hospital_name', 'comment': '医院名称'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_DISEASE',
        'class_name': 'ClmsVerifyDisease',
        'comment': '条款核责配置-疾病范围表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'verifyLevel', 'type': 'Integer', 'column': 'verify_level', 'comment': '核责等级'},
            {'name': 'productCode', 'type': 'String', 'column': 'product_code', 'comment': '产品代码'},
            {'name': 'productName', 'type': 'String', 'column': 'product_name', 'comment': '产品名称'},
            {'name': 'planCode', 'type': 'String', 'column': 'plan_code', 'comment': '险种代码'},
            {'name': 'planName', 'type': 'String', 'column': 'plan_name', 'comment': '险种名称'},
            {'name': 'riskGroupCode', 'type': 'String', 'column': 'risk_group_code', 'comment': '方案代码'},
            {'name': 'riskGroupName', 'type': 'String', 'column': 'risk_group_name', 'comment': '方案名称'},
            {'name': 'dutyCode', 'type': 'String', 'column': 'duty_code', 'comment': '责任代码'},
            {'name': 'dutyName', 'type': 'String', 'column': 'duty_name', 'comment': '责任名称'},
            {'name': 'dutyDetailCode', 'type': 'String', 'column': 'duty_detail_code', 'comment': '责任明细代码'},
            {'name': 'dutyDetailName', 'type': 'String', 'column': 'duty_detail_name', 'comment': '责任明细名称'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_DISEASE_DIR',
        'class_name': 'ClmsVerifyDiseaseDir',
        'comment': '条款核责配置-疾病范围目录表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'vDiseaseId', 'type': 'Integer', 'column': 'v_disease_id', 'comment': '核责疾病范围id'},
            {'name': 'diseaseType', 'type': 'String', 'column': 'disease_type', 'comment': '涵盖疾病类别'},
            {'name': 'dir1Id', 'type': 'Integer', 'column': 'dir1_id', 'comment': '一级疾病目录id'},
            {'name': 'dir1Name', 'type': 'String', 'column': 'dir1_name', 'comment': '一级疾病目录名称'},
            {'name': 'dir2Id', 'type': 'String', 'column': 'dir2_id', 'comment': '二级疾病目录id'},
            {'name': 'dir2Name', 'type': 'String', 'column': 'dir2_name', 'comment': '二级疾病目录名称'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
    {
        'table_name': 'CLMS_VERIFY_DISEASE_EXCLUDE',
        'class_name': 'ClmsVerifyDiseaseExclude',
        'comment': '条款核责配置-疾病范围除外疾病表',
        'fields': [
            {'name': 'id', 'type': 'Integer', 'column': 'id', 'comment': '主键', 'primary': True},
            {'name': 'configId', 'type': 'Integer', 'column': 'config_id', 'comment': '外键'},
            {'name': 'vDiseaseId', 'type': 'Integer', 'column': 'v_disease_id', 'comment': '核责疾病范围id'},
            {'name': 'vddId', 'type': 'Integer', 'column': 'vdd_id', 'comment': '核责疾病目录id'},
            {'name': 'dianoseCode', 'type': 'String', 'column': 'dianose_code', 'comment': '国际ICD编码'},
            {'name': 'diseaseName', 'type': 'String', 'column': 'disease_name', 'comment': '疾病名称'},
            {'name': 'createdBy', 'type': 'String', 'column': 'created_by', 'comment': '创建人'},
            {'name': 'sysCtime', 'type': 'Date', 'column': 'sys_ctime', 'comment': '创建时间'},
            {'name': 'updatedBy', 'type': 'String', 'column': 'updated_by', 'comment': '最新修改人员'},
            {'name': 'sysUtime', 'type': 'Date', 'column': 'sys_utime', 'comment': '最新修改时间'},
        ]
    },
]

def generate_dto_content(table_info):
    """生成DTO类内容"""
    class_name = table_info['class_name']
    comment = table_info['comment']
    fields = table_info['fields']
    
    content = f'''package com.paic.ncbs.claim.dao.autosettle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.math.BigDecimal;

/**
 * {comment}DTO
 * 
 * <AUTHOR> Generated
 * @date {datetime.now().strftime('%Y-%m-%d')}
 */
@Setter
@Getter
@ApiModel("{comment}")
public class {class_name}DTO extends EntityDTO {{

    private static final long serialVersionUID = 1L;

'''
    
    # 生成字段
    for field in fields:
        field_name = field['name']
        field_type = field['type']
        field_comment = field['comment']
        
        content += f'    @ApiModelProperty("{field_comment}")\n'
        
        if field_type == 'Date':
            content += f'    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")\n'
            content += f'    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")\n'
        
        content += f'    private {field_type} {field_name};\n\n'
    
    content += '}'
    
    return content

def generate_mapper_content(table_info):
    """生成Mapper接口内容"""
    class_name = table_info['class_name']
    comment = table_info['comment']
    
    content = f'''package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.{class_name}DTO;
import com.paic.ncbs.claim.dao.base.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * {comment}Mapper接口
 * 
 * <AUTHOR> Generated
 * @date {datetime.now().strftime('%Y-%m-%d')}
 */
@MapperScan
public interface {class_name}Mapper extends BaseDao<{class_name}DTO> {{

    /**
     * 根据创建时间范围查询{comment}
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {comment}列表
     */
    List<{class_name}DTO> selectByCreateTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}}'''
    
    return content

def generate_xml_content(table_info):
    """生成XML映射文件内容"""
    class_name = table_info['class_name']
    table_name = table_info['table_name']
    fields = table_info['fields']
    
    # 生成resultMap
    result_map = f'    <resultMap type="com.paic.ncbs.claim.dao.autosettle.{class_name}DTO" id="{class_name}Map">\n'
    
    # 生成字段映射
    for field in fields:
        field_name = field['name']
        column_name = field['column']
        if field.get('primary'):
            result_map += f'        <id property="{field_name}" column="{column_name}" />\n'
        else:
            result_map += f'        <result property="{field_name}" column="{column_name}" />\n'
    
    result_map += '    </resultMap>'
    
    # 生成列名列表
    column_list = ', '.join([field['column'] for field in fields])
    
    content = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.{class_name}Mapper">

{result_map}

    <sql id="Base_Column_List">
        {column_list}
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="{class_name}Map">
        SELECT 
        <include refid="Base_Column_List" />
        FROM {table_name}
        WHERE id = #{{id,jdbcType=INTEGER}}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.{class_name}DTO" resultMap="{class_name}Map">
        SELECT 
        <include refid="Base_Column_List" />
        FROM {table_name}
        WHERE 1=1
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="{class_name}Map">
        SELECT 
        <include refid="Base_Column_List" />
        FROM {table_name}
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{{startTime}}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{{endTime}}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM {table_name}
        WHERE id = #{{id,jdbcType=INTEGER}}
    </delete>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.{class_name}DTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO {table_name}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <!-- 动态插入字段 -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <!-- 动态插入值 -->
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.{class_name}DTO">
        UPDATE {table_name}
        <set>
            <!-- 动态更新字段 -->
        </set>
        WHERE id = #{{id,jdbcType=INTEGER}}
    </update>

</mapper>'''
    
    return content

def create_directories():
    """创建必要的目录"""
    dirs = [
        'ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle',
        'ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle',
        'ncbs-claim/src/main/resources/mapper/autosettle'
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"创建目录: {dir_path}")

def generate_files():
    """生成所有文件"""
    create_directories()
    
    for table_info in TABLES:
        class_name = table_info['class_name']
        
        # 生成DTO文件
        dto_content = generate_dto_content(table_info)
        dto_path = f'ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/{class_name}DTO.java'
        with open(dto_path, 'w', encoding='utf-8') as f:
            f.write(dto_content)
        print(f"生成DTO文件: {dto_path}")
        
        # 生成Mapper文件
        mapper_content = generate_mapper_content(table_info)
        mapper_path = f'ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/{class_name}Mapper.java'
        with open(mapper_path, 'w', encoding='utf-8') as f:
            f.write(mapper_content)
        print(f"生成Mapper文件: {mapper_path}")
        
        # 生成XML文件
        xml_content = generate_xml_content(table_info)
        xml_path = f'ncbs-claim/src/main/resources/mapper/autosettle/{class_name}Mapper.xml'
        with open(xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        print(f"生成XML文件: {xml_path}")

if __name__ == '__main__':
    print("开始生成autosettle模块代码...")
    generate_files()
    print("代码生成完成！")
    print("\n注意：")
    print("1. 请根据实际需求完善XML文件中的动态SQL部分")
    print("2. 可以根据业务需求在Mapper接口中添加更多查询方法")
    print("3. 建议运行测试确保生成的代码正确")
