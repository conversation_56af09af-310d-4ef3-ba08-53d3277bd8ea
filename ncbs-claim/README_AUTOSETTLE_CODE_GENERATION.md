# AutoSettle模块代码生成说明

## 概述

根据 `create_auto_settle_table.sql` 文件中的表结构，为autosettle模块生成对应的Mapper接口、DTO类和XML映射文件。

## 已生成的文件

### 1. 医院模板包表 (CLMS_HOSPITAL_PACKAGE)
- DTO: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/ClmsHospitalPackageDTO.java`
- Mapper: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/ClmsHospitalPackageMapper.java`
- XML: `ncbs-claim/src/main/resources/mapper/autosettle/ClmsHospitalPackageMapper.xml`

### 2. 药品模板包表 (CLMS_MEDICINE_PACKAGE)
- DTO: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/ClmsMedicinePackageDTO.java`
- Mapper: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/ClmsMedicinePackageMapper.java`
- XML: `ncbs-claim/src/main/resources/mapper/autosettle/ClmsMedicinePackageMapper.xml`

### 3. 药品模板药品表 (CLMS_MEDICINE_PACKAGE_DETAIL)
- DTO: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/ClmsMedicinePackageDetailDTO.java`
- Mapper: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/ClmsMedicinePackageDetailMapper.java`
- XML: `ncbs-claim/src/main/resources/mapper/autosettle/ClmsMedicinePackageDetailMapper.xml`

### 4. 医疗目录表 (CLMS_MEDICAL_DIR)
- DTO: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/autosettle/ClmsMedicalDirDTO.java`
- Mapper: `ncbs-claim/src/main/java/com/paic/ncbs/claim/dao/mapper/autosettle/ClmsMedicalDirMapper.java`
- XML: `ncbs-claim/src/main/resources/mapper/autosettle/ClmsMedicalDirMapper.xml`

## 代码生成工具

### 1. 主要生成脚本
- `generate_autosettle_code.py` - 主要的代码生成脚本
- `complete_table_definitions.py` - 完整的表结构定义文件

### 2. 使用方法

```bash
# 运行代码生成脚本
python generate_autosettle_code.py
```

## 需要生成的剩余表

总共35个表，已生成4个，还需生成31个表：

1. CLMS_MEDICAL_ICD_DIR - 医疗ICD目录关系表
2. CLMS_CLAUSE_CONFIG - 条款理赔配置表
3. CLMS_CLAUSE_TASK - 流程任务表
4. CLMS_CLAUSE_MATCH - 条款匹责配置表
5. CLMS_CLAUSE_MATCH_DISEASE - 条款匹责疾病范围表
6. CLMS_INVOICE_MATCH - 发票匹责结果表
7. CLMS_VERIFY_WAIT_PERIOD - 条款核责配置-等待期表
8. CLMS_VERIFY_HOSPITAL - 条款核责配置-医院范围表
9. CLMS_VERIFY_SPECIFIC_HOSPITAL - 条款核责配置-特定医院表
10. CLMS_VERIFY_DISEASE - 条款核责配置-疾病范围表
11. CLMS_VERIFY_DISEASE_DIR - 条款核责配置-疾病范围目录表
12. CLMS_VERIFY_DISEASE_EXCLUDE - 条款核责配置-疾病范围除外疾病表
13. CLMS_VERIFY_CATALOG3 - 条款核责配置-三目录除外表
14. CLMS_VERIFY_CATALOG3_PACK - 条款核责配置-三目录除外药品包表
15. CLMS_VERIFY_CATALOG3_MEDICINE - 条款核责配置-三目录除外除外药品表
16. CLMS_VERIFY_OTHER_ITEM - 条款核责配置-其他核责项字典表
17. CLMS_VERIFY_OTHER - 条款核责配置-其他核责配置表
18. CLMS_VERIFY_LOG - 自动核责日志主表
19. CLMS_VERIFY_SUBLOG - 自动核责日志子表
20. CLMS_CALC_SEQ - 条款理算配置-理算顺序
21. CLMS_DIFFER_AMOUNT - 条款理算配置-差异化额度表
22. CLMS_DIFFER_AMOUNT_FACTOR - 条款理算配置-差异化额度因子表
23. CLMS_FEE_CONFIG - 条款理算配置-费用约束表
24. CLMS_LIMIT_SHARE - 条款理算配置-费用共享约束表
25. CLMS_LIMIT_SHARE_REL - 条款理算配置-费用共享约束被关联方表
26. CLMS_FEE_SCEN - 条款理算配置-理算费用类场景配置表
27. CLMS_ALLOWANCE_CONFIG - 条款理算配置-津贴类约束表
28. CLMS_ALLOWANCE_SCEN - 条款理算配置-理算津贴类场景配置表
29. CLMS_CACULATE_LOG - 自动理算日志主表
30. CLMS_CACULATE_SUBLOG - 自动理算日志子表

## 代码特点

### DTO类特点
- 继承自 `EntityDTO`
- 使用 Lombok 注解 `@Setter` 和 `@Getter`
- 使用 Swagger 注解 `@ApiModel` 和 `@ApiModelProperty`
- 日期字段使用 `@JsonFormat` 和 `@DateTimeFormat` 注解

### Mapper接口特点
- 继承自 `BaseDao<T>`
- 使用 `@MapperScan` 注解
- 包含常用的查询方法
- 支持按时间范围查询

### XML映射文件特点
- 包含完整的 resultMap 定义
- 提供基础的 CRUD 操作
- 支持动态 SQL 查询
- 包含 insertSelective 和 updateByPrimaryKeySelective 方法

## 注意事项

1. **字段类型映射**：
   - MySQL的 `int` 映射为 Java的 `Integer`
   - MySQL的 `varchar` 映射为 Java的 `String`
   - MySQL的 `datetime` 映射为 Java的 `Date`
   - MySQL的 `boolean` 映射为 Java的 `Boolean`
   - MySQL的 `decimal` 映射为 Java的 `BigDecimal`
   - MySQL的 `json` 映射为 Java的 `String`

2. **命名规范**：
   - 表名：CLMS_XXX_XXX
   - 类名：ClmsXxxXxx
   - 字段名：驼峰命名法

3. **目录结构**：
   - DTO类放在 `com.paic.ncbs.claim.dao.autosettle` 包下
   - Mapper接口放在 `com.paic.ncbs.claim.dao.mapper.autosettle` 包下
   - XML文件放在 `mapper/autosettle` 目录下

## 后续工作

1. 运行代码生成脚本生成剩余的31个表的代码
2. 根据业务需求完善Mapper接口中的查询方法
3. 完善XML文件中的动态SQL部分
4. 编写单元测试验证生成的代码
5. 根据实际业务需求调整字段类型和注解

## 测试建议

生成代码后，建议：
1. 编译检查是否有语法错误
2. 运行单元测试验证基础CRUD操作
3. 检查XML文件中的SQL语句是否正确
4. 验证字段映射是否正确
